{"name": "playcanvas", "version": "2.13.0-beta.0", "author": "PlayCanvas <<EMAIL>>", "homepage": "https://playcanvas.com", "description": "PlayCanvas WebGL game engine", "keywords": ["2d", "3d", "ar", "engine", "gaussian", "game", "gltf", "html5", "javascript", "playcanvas", "splatting", "typescript", "vr", "webgl", "webgl2", "webgpu", "webxr"], "license": "MIT", "main": "build/playcanvas.js", "module": "build/playcanvas/src/index.js", "types": "build/playcanvas.d.ts", "exports": {".": {"types": "./build/playcanvas.d.ts", "development": {"import": "./build/playcanvas.dbg/src/index.js", "require": "./build/playcanvas.dbg.js"}, "profiler": {"import": "./build/playcanvas.prf/src/index.js", "require": "./build/playcanvas.prf.js"}, "production": {"import": "./build/playcanvas/src/index.js", "require": "./build/playcanvas.js"}, "default": {"import": "./build/playcanvas/src/index.js", "require": "./build/playcanvas.js"}}, "./debug": {"types": "./build/playcanvas.d.ts", "import": "./build/playcanvas.dbg/src/index.js", "require": "./build/playcanvas.dbg.js"}, "./profiler": {"types": "./build/playcanvas.d.ts", "import": "./build/playcanvas.prf/src/index.js", "require": "./build/playcanvas.prf.js"}, "./build/*": "./build/*", "./scripts/*": "./scripts/*"}, "sideEffects": ["./build/playcanvas/src/deprecated/deprecated.js"], "type": "module", "bugs": {"url": "https://github.com/playcanvas/engine/issues"}, "repository": {"type": "git", "url": "https://github.com/playcanvas/engine.git"}, "files": ["build/playcanvas*", "build/playcanvas*/*", "scripts", "README*.md"], "dependencies": {"@types/webxr": "^0.5.24", "@webgpu/types": "^0.1.65"}, "devDependencies": {"@playcanvas/eslint-config": "2.1.0", "@rollup/plugin-node-resolve": "16.0.2", "@rollup/plugin-strip": "3.0.4", "@rollup/plugin-swc": "0.4.0", "@rollup/plugin-terser": "0.4.4", "@rollup/pluginutils": "5.3.0", "@swc/core": "1.13.5", "@types/node": "24.7.0", "c8": "10.1.3", "chai": "6.2.0", "eslint": "9.37.0", "fflate": "0.8.2", "globals": "16.4.0", "jsdom": "27.0.0", "mocha": "11.7.4", "publint": "0.3.14", "rollup": "4.52.4", "rollup-plugin-dts": "6.2.3", "rollup-plugin-jscc": "2.0.0", "rollup-plugin-visualizer": "6.0.4", "serve": "14.2.5", "sinon": "19.0.5", "typedoc": "0.28.13", "typedoc-plugin-mdn-links": "5.0.9", "typedoc-plugin-missing-exports": "4.1.0", "typescript": "5.9.3"}, "optionalDependencies": {"canvas": "3.1.0"}, "scripts": {"build": "node build.mjs", "build:release": "npm run build target:release", "build:debug": "npm run build target:debug", "build:profiler": "npm run build target:profiler", "build:types": "npm run build target:types", "build:umd": "npm run build target:umd", "build:esm": "npm run build target:esm", "build:esm:release": "npm run build target:esm:release", "build:esm:debug": "npm run build target:esm:debug", "build:treemap": "npm run build target:release treemap", "build:treenet": "npm run build target:release treenet", "build:treesun": "npm run build target:release treesun", "build:treeflame": "npm run build target:release treeflame", "build:sourcemaps": "npm run build -- -m", "watch": "npm run build -- -w", "watch:release": "npm run build target:release -- -w", "watch:debug": "npm run build target:debug -- -w", "watch:profiler": "npm run build target:profiler -- -w", "watch:umd": "npm run build target:umd -- -w", "watch:esm": "npm run build target:esm -- -w", "watch:esm:release": "npm run build target:esm:release -- -w", "watch:esm:debug": "npm run build target:esm:debug -- -w", "docs": "typedoc", "lint": "eslint scripts src test utils build.mjs eslint.config.mjs rollup.config.mjs", "publint": "publint --level error", "serve": "serve build -l 51000 --cors", "test": "mocha --ignore \"test/assets/scripts/*.js\" --recursive --require test/fixtures.mjs --timeout 5000", "test:coverage": "c8 npm test", "test:types": "tsc --pretty false build/playcanvas.d.ts"}, "engines": {"node": ">=18.0.0"}}